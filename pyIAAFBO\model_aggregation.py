"""
模型聚合机制实现
包括联邦学习中的模型聚合、K-means聚类等功能
"""

import numpy as np
import torch
import torch.nn as nn
from sklearn.cluster import KMeans
from .neural_classifier import PatternNet
import copy


class ModelAggregator:
    """
    模型聚合器
    """
    
    def __init__(self, aggregation_method='fedavg'):
        """
        初始化聚合器
        
        Args:
            aggregation_method: 聚合方法 ('fedavg', 'weighted_avg')
        """
        self.aggregation_method = aggregation_method
    
    def aggregate_models(self, models, client_ids, weights=None):
        """
        聚合多个模型
        
        Args:
            models: 模型字典 {client_id: model}
            client_ids: 要聚合的客户端ID列表
            weights: 聚合权重，如果为None则使用平均权重
            
        Returns:
            aggregated_model: 聚合后的模型
        """
        if not client_ids:
            return None
        
        if len(client_ids) == 1:
            # 只有一个模型，直接返回副本
            return copy.deepcopy(models[client_ids[0]])
        
        # 获取第一个模型作为模板
        template_model = models[client_ids[0]]
        
        # 创建聚合后的模型
        if hasattr(template_model, 'net'):
            # 如果是NeuralClassifier对象
            aggregated_model = copy.deepcopy(template_model)
            aggregated_net = aggregated_model.net
        else:
            # 如果是PatternNet对象
            aggregated_net = PatternNet(
                template_model.input_size,
                template_model.hidden_sizes
            )
            aggregated_model = aggregated_net
        
        # 设置权重
        if weights is None:
            weights = np.ones(len(client_ids)) / len(client_ids)
        else:
            weights = np.array(weights)
            weights = weights / np.sum(weights)  # 归一化
        
        # 聚合参数
        self._aggregate_parameters(models, client_ids, aggregated_net, weights)
        
        return aggregated_model
    
    def _aggregate_parameters(self, models, client_ids, target_model, weights):
        """
        聚合模型参数
        
        Args:
            models: 源模型字典
            client_ids: 客户端ID列表
            target_model: 目标模型
            weights: 聚合权重
        """
        # 获取目标模型的参数
        target_params = list(target_model.parameters())
        
        # 初始化聚合参数
        for param in target_params:
            param.data.zero_()
        
        # 加权聚合
        for i, client_id in enumerate(client_ids):
            source_model = models[client_id]
            
            # 获取源模型的网络
            if hasattr(source_model, 'net'):
                source_net = source_model.net
            else:
                source_net = source_model
            
            source_params = list(source_net.parameters())
            
            # 加权累加参数
            for target_param, source_param in zip(target_params, source_params):
                target_param.data += weights[i] * source_param.data
    
    def aggregate_weights_vector(self, weight_vectors, weights=None):
        """
        聚合权重向量
        
        Args:
            weight_vectors: 权重向量列表
            weights: 聚合权重
            
        Returns:
            aggregated_vector: 聚合后的权重向量
        """
        weight_vectors = [torch.tensor(w) if not isinstance(w, torch.Tensor) else w 
                         for w in weight_vectors]
        
        if weights is None:
            weights = torch.ones(len(weight_vectors)) / len(weight_vectors)
        else:
            weights = torch.tensor(weights)
            weights = weights / torch.sum(weights)
        
        # 加权平均
        aggregated_vector = torch.zeros_like(weight_vectors[0])
        for i, weight_vector in enumerate(weight_vectors):
            aggregated_vector += weights[i] * weight_vector
        
        return aggregated_vector


class ModelClusterer:
    """
    模型聚类器
    """
    
    def __init__(self, n_clusters=6, clustering_method='kmeans'):
        """
        初始化聚类器
        
        Args:
            n_clusters: 聚类数量
            clustering_method: 聚类方法
        """
        self.n_clusters = n_clusters
        self.clustering_method = clustering_method
    
    def extract_weight_vectors(self, models, client_ids):
        """
        提取模型权重向量
        
        Args:
            models: 模型字典
            client_ids: 客户端ID列表
            
        Returns:
            weight_matrix: 权重矩阵 (n_clients x n_features)
        """
        weight_vectors = []
        
        for client_id in client_ids:
            model = models[client_id]
            
            # 获取网络
            if hasattr(model, 'net'):
                net = model.net
            else:
                net = model
            
            # 提取权重向量
            weight_vector = self._convert_model_para_to_vector(net)
            weight_vectors.append(weight_vector)
        
        return np.array(weight_vectors)
    
    def _convert_model_para_to_vector(self, model):
        """
        将模型参数转换为向量
        
        Args:
            model: 神经网络模型
            
        Returns:
            weight_vector: 权重向量
        """
        weight_vector = []
        
        # 提取输入层权重
        if hasattr(model, 'network'):
            # PatternNet结构
            layers = model.network
            for layer in layers:
                if isinstance(layer, nn.Linear):
                    # 权重矩阵
                    weight_matrix = layer.weight.data.cpu().numpy()
                    weight_vector.extend(weight_matrix.flatten())
                    
                    # 偏置向量
                    if layer.bias is not None:
                        bias_vector = layer.bias.data.cpu().numpy()
                        weight_vector.extend(bias_vector.flatten())
        else:
            # 直接从参数提取
            for param in model.parameters():
                weight_vector.extend(param.data.cpu().numpy().flatten())
        
        return np.array(weight_vector)
    
    def cluster_models(self, models, client_ids):
        """
        对模型进行聚类
        
        Args:
            models: 模型字典
            client_ids: 客户端ID列表
            
        Returns:
            cluster_assignments: 聚类分配结果
            cluster_centers: 聚类中心
        """
        # 提取权重向量
        weight_matrix = self.extract_weight_vectors(models, client_ids)
        
        # 执行聚类
        if self.clustering_method == 'kmeans':
            kmeans = KMeans(n_clusters=self.n_clusters, random_state=42, n_init=10)
            cluster_assignments = kmeans.fit_predict(weight_matrix)
            cluster_centers = kmeans.cluster_centers_
        else:
            raise ValueError(f"Unsupported clustering method: {self.clustering_method}")
        
        return cluster_assignments, cluster_centers
    
    def get_cluster_members(self, cluster_assignments, client_ids):
        """
        获取每个聚类的成员
        
        Args:
            cluster_assignments: 聚类分配结果
            client_ids: 客户端ID列表
            
        Returns:
            cluster_members: 字典，键为聚类ID，值为客户端ID列表
        """
        cluster_members = {}
        
        for i, cluster_id in enumerate(cluster_assignments):
            if cluster_id not in cluster_members:
                cluster_members[cluster_id] = []
            cluster_members[cluster_id].append(client_ids[i])
        
        return cluster_members


def model_agg(models, client_ids):
    """
    模型聚合函数，与MATLAB版本兼容
    
    Args:
        models: 模型字典
        client_ids: 要聚合的客户端ID列表
        
    Returns:
        aggregated_model: 聚合后的模型
    """
    aggregator = ModelAggregator()
    return aggregator.aggregate_models(models, client_ids)


def convert_model_para_to_vector(models, client_id):
    """
    将模型参数转换为向量，与MATLAB版本兼容
    
    Args:
        models: 模型字典
        client_id: 客户端ID
        
    Returns:
        weight_vector: 权重向量
    """
    clusterer = ModelClusterer()
    return clusterer._convert_model_para_to_vector(
        models[client_id].net if hasattr(models[client_id], 'net') else models[client_id]
    )


class FederatedAggregationStrategy:
    """
    联邦聚合策略
    """
    
    def __init__(self, strategy='cluster_based'):
        """
        初始化策略
        
        Args:
            strategy: 聚合策略 ('cluster_based', 'similarity_based', 'random')
        """
        self.strategy = strategy
        self.aggregator = ModelAggregator()
        self.clusterer = ModelClusterer()
    
    def select_aggregation_partners(self, models, client_id, client_ids, 
                                  cluster_assignments=None, max_partners=3):
        """
        选择聚合伙伴
        
        Args:
            models: 模型字典
            client_id: 当前客户端ID
            client_ids: 所有客户端ID列表
            cluster_assignments: 聚类分配结果
            max_partners: 最大伙伴数量
            
        Returns:
            partner_ids: 伙伴客户端ID列表
        """
        if self.strategy == 'cluster_based' and cluster_assignments is not None:
            # 基于聚类的选择
            client_idx = client_ids.index(client_id)
            client_cluster = cluster_assignments[client_idx]
            
            # 找到同一聚类的其他客户端
            partner_ids = []
            for i, cluster_id in enumerate(cluster_assignments):
                if cluster_id == client_cluster and client_ids[i] != client_id:
                    partner_ids.append(client_ids[i])
            
            # 限制伙伴数量
            if len(partner_ids) > max_partners:
                partner_ids = np.random.choice(partner_ids, max_partners, replace=False).tolist()
            
            # 包含自己
            partner_ids.append(client_id)
            
        elif self.strategy == 'random':
            # 随机选择
            available_partners = [cid for cid in client_ids if cid != client_id]
            n_partners = min(max_partners, len(available_partners))
            partner_ids = np.random.choice(available_partners, n_partners, replace=False).tolist()
            partner_ids.append(client_id)
            
        else:
            # 默认：只使用自己的模型
            partner_ids = [client_id]
        
        return partner_ids
