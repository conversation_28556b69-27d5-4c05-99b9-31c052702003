"""
IAFFBO算法配置文件
包含所有算法参数、客户端数量、维度等设置
"""

import numpy as np
import torch


class IAFFBOConfig:
    """
    IAFFBO算法配置类
    """
    
    def __init__(self):
        """
        初始化默认配置，与MATLAB版本完全一致
        """
        # 基本实验参数
        self.runs = 20                    # 运行次数
        self.internal_runs = 1            # 内部运行次数
        self.client_num = 18              # 客户端数量（对应18个任务）
        self.dim = 10                     # 问题维度
        
        # 采样参数
        self.n_initial = 50               # 初始采样点数量 (N)
        self.max_fe = 60                  # 最大函数评估次数 (MAXFE)
        
        # 获取函数参数
        self.ucb_flag = 2                 # 获取函数类型: 0=EI, 1=UCB, 2=LCB
        
        # 隐私保护参数
        self.privacy_prob = 0.0           # 隐私噪声概率 (p)
        
        # 聚类参数
        self.cluster_num = 6              # 聚类数量 (cl_num)
        
        # 迁移学习参数
        self.flag_transfer = 1            # 是否启用迁移学习
        
        # 竞争群体优化参数
        self.pop_size = 100               # 种群大小 (popsize)
        self.max_iter = 100               # 最大迭代次数 (wmax)
        self.phi = 0.1                    # 速度更新参数
        
        # 高斯过程参数
        self.theta_init = 5.0             # 相关参数初始值
        self.theta_lower = 1e-5           # 相关参数下界
        self.theta_upper = 100.0          # 相关参数上界
        self.n_candidates = 100           # 候选点数量 (N_notR_ini)
        
        # 神经网络参数
        self.nn_learning_rate = 0.01      # 学习率
        self.nn_max_epochs = 1000         # 最大训练轮数
        self.nn_patience = 50             # 早停耐心值
        self.nn_hidden_ratio = [1.5, 1.0, 0.5]  # 隐藏层大小比例
        
        # 数据处理参数
        self.train_ratio = 0.75           # 训练集比例
        
        # 失败检测参数
        self.max_fail_count = 3           # 最大失败次数
        
        # 随机种子
        self.random_seed = None           # None表示随机种子
        
        # 输出参数
        self.verbose = True               # 是否打印详细信息
        self.save_results = True          # 是否保存结果
        self.result_dir = 'results'       # 结果保存目录
        
        # 性能优化选项
        self.use_parallel = True          # 启用并行处理
        self.n_workers = 4                # 并行工作线程数
        self.enable_cache = True          # 启用缓存
        self.cache_size = 128             # 缓存大小
        self.use_fast_compute = True      # 使用快速数值计算
        self.compile_models = True        # 编译神经网络模型
        
        # GPU 加速选项
        self.use_gpu = torch.cuda.is_available()  # 自动检测GPU
        self.gpu_device = 'cuda:0'        # GPU设备
    
    def to_dict(self):
        """
        转换为字典格式
        
        Returns:
            config_dict: 配置字典
        """
        return {
            'runs': self.runs,
            'internal_runs': self.internal_runs,
            'client_num': self.client_num,
            'dim': self.dim,
            'n_initial': self.n_initial,
            'max_fe': self.max_fe,
            'ucb_flag': self.ucb_flag,
            'privacy_prob': self.privacy_prob,
            'cluster_num': self.cluster_num,
            'flag_transfer': self.flag_transfer,
            'pop_size': self.pop_size,
            'max_iter': self.max_iter,
            'phi': self.phi,
            'theta_init': self.theta_init,
            'theta_lower': self.theta_lower,
            'theta_upper': self.theta_upper,
            'n_candidates': self.n_candidates,
            'nn_learning_rate': self.nn_learning_rate,
            'nn_max_epochs': self.nn_max_epochs,
            'nn_patience': self.nn_patience,
            'nn_hidden_ratio': self.nn_hidden_ratio,
            'train_ratio': self.train_ratio,
            'max_fail_count': self.max_fail_count,
            'random_seed': self.random_seed,
            'verbose': self.verbose,
            'save_results': self.save_results,
            'result_dir': self.result_dir,
            'use_parallel': self.use_parallel,
            'n_workers': self.n_workers,
            'enable_cache': self.enable_cache,
            'cache_size': self.cache_size,
            'use_fast_compute': self.use_fast_compute,
            'compile_models': self.compile_models,
            'use_gpu': self.use_gpu,
            'gpu_device': self.gpu_device
        }
    
    def update_from_dict(self, config_dict):
        """
        从字典更新配置
        
        Args:
            config_dict: 配置字典
        """
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def get_experiment_name(self):
        """
        生成实验名称
        
        Returns:
            experiment_name: 实验名称字符串
        """
        return (f"IAFFBO_{self.client_num}tasks_p{self.privacy_prob}_"
                f"AF{self.ucb_flag}_D{self.dim}_MAXFE{self.max_fe + self.n_initial}_"
                f"trs{self.flag_transfer}_clu{self.cluster_num}")


# 预定义配置
DEFAULT_CONFIG = IAFFBOConfig()

# 不同实验设置的配置
EXPERIMENT_CONFIGS = {
    'default': IAFFBOConfig(),
    
    'high_privacy': {
        'privacy_prob': 0.3,
        'cluster_num': 8
    },
    
    'no_transfer': {
        'flag_transfer': 0,
        'cluster_num': 1
    },
    
    'ucb_acquisition': {
        'ucb_flag': 1
    },
    
    'ei_acquisition': {
        'ucb_flag': 0
    },
    
    'small_scale': {
        'client_num': 6,
        'dim': 5,
        'max_fe': 50,
        'cluster_num': 3
    },
    
    'large_scale': {
        'client_num': 24,
        'dim': 20,
        'max_fe': 150,
        'cluster_num': 8
    }
}


def get_config(config_name='default', custom_params=None):
    """获取配置，添加性能优化选项但不改变算法参数"""
    
    if config_name == 'default':
        config = IAFFBOConfig()
        config.client_num = 6
        config.dim = 10
        config.n_initial = 50  # 对应MATLAB中的N=50
        config.max_fe = 60     # 对应MATLAB中的MAXFE=60
        config.ucb_flag = 2    # 对应MATLAB中的UCB_Flag=2 (LCB)
        config.flag_transfer = 1
        config.cluster_num = 6  # 对应MATLAB中的cl_num=6
        config.runs = 20       # 对应MATLAB中的runs=20
        
        # 添加性能优化选项，不影响算法逻辑
        config.use_parallel = True
        config.n_workers = min(4, config.client_num)
        config.use_gpu = torch.cuda.is_available()
        config.compile_models = True
        
        # 保持所有原有算法参数不变
        config.p = 0  # 对应MATLAB中的p=0（隐私噪声参数）
        config.n_not_r_ini = 100  # 对应MATLAB中的N_notR_ini=100
    else:
        config = IAFFBOConfig()
        if config_name in EXPERIMENT_CONFIGS:
            config.update_from_dict(EXPERIMENT_CONFIGS[config_name])
    
    # 应用自定义参数
    if custom_params:
        config.update_from_dict(custom_params)
    
    return config


def validate_config(config):
    """
    验证配置参数的有效性
    
    Args:
        config: 配置对象
        
    Returns:
        is_valid: 是否有效
        error_messages: 错误信息列表
    """
    error_messages = []
    
    # 检查基本参数
    if config.client_num <= 0:
        error_messages.append("client_num must be positive")
    
    if config.dim <= 0:
        error_messages.append("dim must be positive")
    
    if config.n_initial <= 0:
        error_messages.append("n_initial must be positive")
    
    if config.max_fe <= 0:
        error_messages.append("max_fe must be positive")
    
    # 检查获取函数参数
    if config.ucb_flag not in [0, 1, 2]:
        error_messages.append("ucb_flag must be 0, 1, or 2")
    
    # 检查隐私参数
    if not (0 <= config.privacy_prob <= 1):
        error_messages.append("privacy_prob must be between 0 and 1")
    
    # 检查聚类参数
    if config.cluster_num <= 0:
        error_messages.append("cluster_num must be positive")
    
    if config.cluster_num > config.client_num:
        error_messages.append("cluster_num should not exceed client_num")
    
    # 检查优化器参数
    if config.pop_size <= 0:
        error_messages.append("pop_size must be positive")
    
    if config.max_iter <= 0:
        error_messages.append("max_iter must be positive")
    
    if config.phi <= 0:
        error_messages.append("phi must be positive")
    
    # 检查GP参数
    if config.theta_init <= 0:
        error_messages.append("theta_init must be positive")
    
    if config.theta_lower <= 0:
        error_messages.append("theta_lower must be positive")
    
    if config.theta_upper <= config.theta_lower:
        error_messages.append("theta_upper must be greater than theta_lower")
    
    # 检查神经网络参数
    if config.nn_learning_rate <= 0:
        error_messages.append("nn_learning_rate must be positive")
    
    if config.nn_max_epochs <= 0:
        error_messages.append("nn_max_epochs must be positive")
    
    if config.nn_patience <= 0:
        error_messages.append("nn_patience must be positive")
    
    # 检查数据处理参数
    if not (0 < config.train_ratio < 1):
        error_messages.append("train_ratio must be between 0 and 1")
    
    is_valid = len(error_messages) == 0
    
    return is_valid, error_messages


def print_config(config):
    """
    打印配置信息
    
    Args:
        config: 配置对象
    """
    print("=" * 50)
    print("IAFFBO Algorithm Configuration")
    print("=" * 50)
    
    print(f"Experiment: {config.get_experiment_name()}")
    print()
    
    print("Basic Parameters:")
    print(f"  Clients: {config.client_num}")
    print(f"  Dimensions: {config.dim}")
    print(f"  Initial samples: {config.n_initial}")
    print(f"  Max evaluations: {config.max_fe}")
    print(f"  Total evaluations: {config.n_initial + config.max_fe}")
    print()
    
    print("Algorithm Parameters:")
    acq_names = {0: 'EI', 1: 'UCB', 2: 'LCB'}
    print(f"  Acquisition function: {acq_names[config.ucb_flag]}")
    print(f"  Privacy probability: {config.privacy_prob}")
    print(f"  Transfer learning: {'Enabled' if config.flag_transfer else 'Disabled'}")
    print(f"  Clusters: {config.cluster_num}")
    print()
    
    print("Optimization Parameters:")
    print(f"  Population size: {config.pop_size}")
    print(f"  Max iterations: {config.max_iter}")
    print(f"  Phi parameter: {config.phi}")
    print()
    
    print("GP Parameters:")
    print(f"  Theta init: {config.theta_init}")
    print(f"  Theta bounds: [{config.theta_lower}, {config.theta_upper}]")
    print()
    
    print("Neural Network Parameters:")
    print(f"  Learning rate: {config.nn_learning_rate}")
    print(f"  Max epochs: {config.nn_max_epochs}")
    print(f"  Patience: {config.nn_patience}")
    print()
    
    print("=" * 50)


