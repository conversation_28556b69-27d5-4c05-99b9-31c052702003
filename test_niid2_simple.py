"""
简化的NIID2测试脚本
用于诊断问题
"""

import os
import sys
import numpy as np

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from pyIAAFBO.Tasks.benchmark import create_tasks_diff_func
from pyIAAFBO.utils.partitions import create_normalized_partitions
from pyIAAFBO.utils.initialize import init_samples


def test_niid2_setup():
    """测试NIID2设置"""
    print("测试NIID2设置...")
    
    # 参数
    dim = 10
    client_num = 18
    n_initial = 50
    
    print(f"维度: {dim}, 客户端数: {client_num}")
    
    # 创建任务
    print("创建任务...")
    try:
        tasks = create_tasks_diff_func(dim, normalized=False)
        print(f"成功创建{len(tasks)}个任务")
        
        # 检查任务维度
        for i, task in enumerate(tasks[:5]):  # 只检查前5个
            task_dim = getattr(task, 'd', 'unknown')
            print(f"任务{i}: 维度={task_dim}, x_lb={task.x_lb}, x_ub={task.x_ub}")
            
    except Exception as e:
        print(f"创建任务失败: {e}")
        return False
    
    # 创建分区
    print("\n创建分区...")
    try:
        np_per_dim = 2  # niid2
        partitions = create_normalized_partitions(client_num, np_per_dim, dim)
        print(f"成功创建{len(partitions)}个分区")
        print(f"分区示例: {partitions[0]}")
        
    except Exception as e:
        print(f"创建分区失败: {e}")
        return False
    
    # 测试采样
    print("\n测试采样...")
    try:
        task = tasks[0]
        partition = partitions[0]
        
        print(f"任务维度: {getattr(task, 'd', 'unknown')}")
        print(f"分区: {partition}")
        
        # 测试非IID采样
        initial_x = init_samples(
            xlb=task.x_lb, 
            xub=task.x_ub, 
            normalized=False,
            size=n_initial,
            d=dim,
            iid=False,
            partitions=partition
        )
        
        print(f"采样结果形状: {initial_x.shape}")
        print(f"采样示例: {initial_x[0]}")
        
        # 测试任务评估
        print("\n测试任务评估...")
        test_x = initial_x[0]
        print(f"测试输入: {test_x}, 长度: {len(test_x)}")
        
        result = task(test_x)
        print(f"评估结果: {result}")
        
    except Exception as e:
        print(f"采样或评估失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n✅ NIID2设置测试成功!")
    return True


def test_single_run():
    """测试单次运行"""
    print("\n" + "="*50)
    print("测试单次NIID2运行")
    print("="*50)
    
    # 导入必要的模块
    from pyIAAFBO.config import get_config
    from pyIAAFBO.iaffbo_algorithm import IAFFBOAlgorithm
    
    # 简化配置
    custom_params = {
        'runs': 1,
        'client_num': 3,  # 减少到3个客户端
        'dim': 5,         # 减少到5维
        'n_initial': 10,  # 减少初始样本
        'max_fe': 5,      # 减少评估次数
        'ucb_flag': 2,
        'privacy_prob': 0.0,
        'flag_transfer': 1,
        'cluster_num': 3,
        'verbose': True,
        'use_gpu': False,  # 先不用GPU
        'use_parallel': False,  # 先不用并行
    }
    
    config = get_config('default', custom_params)
    
    # 创建任务和分区
    tasks = create_tasks_diff_func(config.dim, normalized=False)[:config.client_num]
    partitions = create_normalized_partitions(config.client_num, 2, config.dim)
    
    print(f"任务数: {len(tasks)}, 分区数: {len(partitions)}")
    
    # 创建算法实例
    algorithm = IAFFBOAlgorithm(config.to_dict())
    algorithm.set_tasks(tasks)
    
    # 手动设置初始数据
    print("设置初始数据...")
    for client_id in range(config.client_num):
        task = tasks[client_id]
        
        # 使用简单的随机采样
        from pyDOE import lhs
        initial_x = lhs(config.dim, samples=config.n_initial)
        
        # 缩放到任务边界
        if np.isscalar(task.x_lb):
            initial_x = initial_x * (task.x_ub - task.x_lb) + task.x_lb
        else:
            initial_x = initial_x * (np.array(task.x_ub) - np.array(task.x_lb)) + np.array(task.x_lb)
        
        # 评估
        initial_y = []
        for x in initial_x:
            try:
                y = task(x)
                initial_y.append(y)
            except Exception as e:
                print(f"评估失败: {e}")
                continue
        
        initial_y = np.array(initial_y)
        
        algorithm.initial_data[client_id] = {
            'x': initial_x[:len(initial_y)],
            'y': initial_y
        }
        
        print(f"客户端{client_id}: {len(initial_y)}个初始样本")
    
    # 运行优化
    print("开始优化...")
    try:
        results = algorithm.run_optimization_parallel()
        print("✅ 优化完成!")
        print(f"最终结果: {results['best_values'][-1] if results['best_values'] else 'None'}")
        return True
        
    except Exception as e:
        print(f"❌ 优化失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("NIID2诊断测试")
    print("="*50)
    
    # 测试基本设置
    if test_niid2_setup():
        # 测试单次运行
        test_single_run()
    else:
        print("❌ 基本设置测试失败")
