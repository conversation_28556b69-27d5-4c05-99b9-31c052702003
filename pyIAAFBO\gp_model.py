"""
高斯过程模型实现，基于DACE工具箱
实现与MATLAB版本完全一致的GP拟合和预测功能
优化版本：支持GPU加速和并行计算
"""

import numpy as np
import torch
from numba import jit, cuda
from scipy.optimize import minimize
from scipy.linalg import cholesky, solve_triangular
from scipy.spatial.distance import pdist, squareform
from concurrent.futures import ThreadPoolExecutor
import warnings

@jit(nopython=True, cache=True)
def fast_correlation_matrix(X, theta):
    """使用 Numba 加速相关矩阵计算"""
    n = X.shape[0]
    R = np.ones((n, n))

    for i in range(n):
        for j in range(i+1, n):
            r = 0.0
            for k in range(X.shape[1]):
                r += theta[k] * (X[i, k] - X[j, k])**2
            R[i, j] = np.exp(-r)
            R[j, i] = R[i, j]

    return R

def gpu_correlation_matrix(X, theta, device='auto'):
    """使用GPU加速相关矩阵计算"""
    if device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device)

    # 转换为torch tensor
    X_tensor = torch.tensor(X, dtype=torch.float32, device=device)
    theta_tensor = torch.tensor(theta, dtype=torch.float32, device=device)

    n = X_tensor.shape[0]

    # 计算距离矩阵
    X_expanded = X_tensor.unsqueeze(1)  # (n, 1, d)
    X_expanded_T = X_tensor.unsqueeze(0)  # (1, n, d)

    # 计算平方距离
    diff = X_expanded - X_expanded_T  # (n, n, d)
    sq_diff = diff ** 2  # (n, n, d)

    # 加权距离
    weighted_dist = torch.sum(sq_diff * theta_tensor.unsqueeze(0).unsqueeze(0), dim=2)  # (n, n)

    # 相关矩阵
    R = torch.exp(-weighted_dist)

    return R.cpu().numpy()

def gpu_predict_batch(X_pred, X_train, theta, gamma, sigma2, beta, Ft, device='auto', batch_size=1000):
    """GPU批量预测"""
    if device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device)

    n_pred = X_pred.shape[0]
    n_train = X_train.shape[0]

    # 转换为torch tensor
    X_pred_tensor = torch.tensor(X_pred, dtype=torch.float32, device=device)
    X_train_tensor = torch.tensor(X_train, dtype=torch.float32, device=device)
    theta_tensor = torch.tensor(theta, dtype=torch.float32, device=device)
    gamma_tensor = torch.tensor(gamma, dtype=torch.float32, device=device)

    y_pred = np.zeros(n_pred)
    mse_pred = np.zeros(n_pred)

    # 批处理预测
    for i in range(0, n_pred, batch_size):
        end_idx = min(i + batch_size, n_pred)
        batch_X = X_pred_tensor[i:end_idx]

        # 计算相关向量
        batch_expanded = batch_X.unsqueeze(1)  # (batch, 1, d)
        train_expanded = X_train_tensor.unsqueeze(0)  # (1, n_train, d)

        diff = batch_expanded - train_expanded  # (batch, n_train, d)
        sq_diff = diff ** 2
        weighted_dist = torch.sum(sq_diff * theta_tensor.unsqueeze(0).unsqueeze(0), dim=2)
        r = torch.exp(-weighted_dist)  # (batch, n_train)

        # 预测均值
        if Ft.shape[1] == 1:  # regpoly0
            f = torch.ones(end_idx - i, 1, device=device)
        else:  # regpoly1
            f = torch.cat([torch.ones(end_idx - i, 1, device=device), batch_X], dim=1)

        y_batch = (f @ torch.tensor(beta, dtype=torch.float32, device=device).unsqueeze(-1) +
                   r @ gamma_tensor.unsqueeze(-1)).squeeze(-1)

        y_pred[i:end_idx] = y_batch.cpu().numpy()

        # 预测方差（简化计算）
        mse_pred[i:end_idx] = sigma2

    return y_pred, mse_pred

class GaussianProcess:
    """
    高斯过程模型，实现DACE工具箱的功能
    """
    
    def __init__(self, regr='regpoly0', corr='corrgauss', theta0=None,
                 lob=None, upb=None, normalize=True, use_gpu=True):
        """
        初始化GP模型

        Args:
            regr: 回归函数类型 ('regpoly0' 或 'regpoly1')
            corr: 相关函数类型 ('corrgauss')
            theta0: 相关参数初始值
            lob: 参数下界
            upb: 参数上界
            normalize: 是否归一化
            use_gpu: 是否使用GPU加速
        """
        self.regr = regr
        self.corr = corr
        self.theta0 = theta0
        self.lob = lob
        self.upb = upb
        self.normalize = normalize
        self.use_fast_compute = True
        self.use_gpu = use_gpu and torch.cuda.is_available()
        self.device = torch.device('cuda' if self.use_gpu else 'cpu')
        
        # 模型参数
        self.theta = None
        self.beta = None
        self.gamma = None
        self.sigma2 = None
        self.S = None
        self.Y = None
        self.C = None
        self.Ft = None
        self.G = None
        
        # 归一化参数
        self.S_mean = None
        self.S_std = None
        self.Y_mean = None
        self.Y_std = None
        
    def regpoly0(self, S):
        """常数回归函数"""
        m = S.shape[0]
        return np.ones((m, 1))
    
    def regpoly1(self, S):
        """一阶多项式回归函数"""
        m, n = S.shape
        F = np.ones((m, n + 1))
        F[:, 1:] = S
        return F
    
    def corrgauss(self, theta, d):
        """高斯相关函数"""
        theta = np.asarray(theta)
        d = np.asarray(d)
        
        if d.ndim == 1:
            d = d.reshape(1, -1)
            
        # 计算相关矩阵
        td = d**2 * theta.reshape(1, -1)
        r = np.exp(-np.sum(td, axis=1))
        
        return r
    
    def _normalize_data(self, S, Y):
        """数据归一化"""
        if self.normalize:
            self.S_mean = np.mean(S, axis=0)
            self.S_std = np.std(S, axis=0)
            self.S_std[self.S_std == 0] = 1  # 避免除零
            
            self.Y_mean = np.mean(Y)
            self.Y_std = np.std(Y)
            if self.Y_std == 0:
                self.Y_std = 1
                
            S_norm = (S - self.S_mean) / self.S_std
            Y_norm = (Y - self.Y_mean) / self.Y_std
        else:
            S_norm = S.copy()
            Y_norm = Y.copy()
            
        return S_norm, Y_norm
    
    def _denormalize_prediction(self, y_pred, mse_pred):
        """反归一化预测结果"""
        if self.normalize:
            y_pred = y_pred * self.Y_std + self.Y_mean
            mse_pred = mse_pred * (self.Y_std**2)
        return y_pred, mse_pred
    
    def _likelihood(self, theta, S, Y, F):
        """计算负对数似然函数"""
        try:
            theta = np.asarray(theta)
            m, n = S.shape
            
            # 计算距离矩阵
            distances = pdist(S, metric='euclidean')
            D = squareform(distances)
            
            # 计算相关矩阵
            R = np.zeros((m, m))
            for i in range(m):
                for j in range(m):
                    if i == j:
                        R[i, j] = 1.0
                    else:
                        d = S[i, :] - S[j, :]
                        R[i, j] = self.corrgauss(theta, d)
            
            # 添加数值稳定性
            R += np.eye(m) * 1e-10
            
            # Cholesky分解
            try:
                C = cholesky(R, lower=True)
            except np.linalg.LinAlgError:
                return 1e10
            
            # 求解线性系统
            Ft = solve_triangular(C, F, lower=True)
            Yt = solve_triangular(C, Y, lower=True)
            
            # QR分解
            Q, G = np.linalg.qr(Ft)
            
            # 计算beta
            if G.shape[0] > 0:
                beta = solve_triangular(G, Q.T @ Yt, lower=False)
            else:
                beta = np.array([np.mean(Y)])
            
            # 计算残差
            rho = Yt - Ft @ beta
            
            # 计算sigma2
            sigma2 = np.sum(rho**2) / m
            
            # 计算负对数似然
            detR = 2 * np.sum(np.log(np.diag(C)))
            nll = 0.5 * (m * np.log(sigma2) + detR)
            
            return nll
            
        except:
            return 1e10
    
    def fit(self, S, Y):
        """
        拟合GP模型
        
        Args:
            S: 输入数据 (m x n)
            Y: 输出数据 (m x 1)
        """
        S = np.asarray(S)
        Y = np.asarray(Y).flatten()
        
        if S.ndim == 1:
            S = S.reshape(-1, 1)
        
        m, n = S.shape
        
        # 数据归一化
        S_norm, Y_norm = self._normalize_data(S, Y)
        
        # 设置初始参数
        if self.theta0 is None:
            self.theta0 = 5.0 * np.ones(n)
        
        if self.lob is None:
            self.lob = 1e-5 * np.ones(n)
            
        if self.upb is None:
            self.upb = 100.0 * np.ones(n)
        
        # 回归矩阵
        if self.regr == 'regpoly0':
            F = self.regpoly0(S_norm)
        else:
            F = self.regpoly1(S_norm)
        
        # 优化超参数
        bounds = list(zip(self.lob, self.upb))
        
        result = minimize(
            self._likelihood,
            self.theta0,
            args=(S_norm, Y_norm, F),
            method='L-BFGS-B',
            bounds=bounds
        )
        
        self.theta = result.x
        
        # 使用最优参数重新计算模型参数
        self._compute_model_parameters(S_norm, Y_norm, F)
        
        return self
    
    def _compute_model_parameters(self, S, Y, F):
        """计算模型参数"""
        m, n = S.shape
        
        # 计算相关矩阵
        R = np.zeros((m, m))
        for i in range(m):
            for j in range(m):
                if i == j:
                    R[i, j] = 1.0
                else:
                    d = S[i, :] - S[j, :]
                    R[i, j] = self.corrgauss(self.theta, d)
        
        # 添加数值稳定性
        R += np.eye(m) * 1e-10
        
        # Cholesky分解
        self.C = cholesky(R, lower=True)
        
        # 求解线性系统
        self.Ft = solve_triangular(self.C, F, lower=True)
        Yt = solve_triangular(self.C, Y, lower=True)
        
        # QR分解
        Q, self.G = np.linalg.qr(self.Ft)
        
        # 计算beta
        if self.G.shape[0] > 0:
            self.beta = solve_triangular(self.G, Q.T @ Yt, lower=False)
        else:
            self.beta = np.array([np.mean(Y)])
        
        # 计算gamma
        self.gamma = solve_triangular(self.C, Y - F @ self.beta, lower=True)
        
        # 计算sigma2
        self.sigma2 = np.sum(self.gamma**2) / m
        
        # 保存训练数据
        self.S = S
        self.Y = Y
    
    def predict(self, S_new, use_gpu_batch=True, batch_size=1000):
        """
        预测新点，支持GPU加速批处理

        Args:
            S_new: 新的输入点
            use_gpu_batch: 是否使用GPU批处理
            batch_size: 批处理大小

        Returns:
            y_pred: 预测均值
            mse_pred: 预测方差
        """
        S_new = np.asarray(S_new)
        if S_new.ndim == 1:
            S_new = S_new.reshape(1, -1)

        # 归一化新数据
        if self.normalize:
            S_new_norm = (S_new - self.S_mean) / self.S_std
        else:
            S_new_norm = S_new.copy()

        m_new = S_new_norm.shape[0]

        # 如果使用GPU且数据量大，使用批处理
        if self.use_gpu and use_gpu_batch and m_new > 10:
            try:
                y_pred, mse_pred = gpu_predict_batch(
                    S_new_norm, self.S, self.theta, self.gamma,
                    self.sigma2, self.beta, self.Ft,
                    device=self.device, batch_size=batch_size
                )
            except Exception as e:
                warnings.warn(f"GPU prediction failed, falling back to CPU: {e}")
                y_pred, mse_pred = self._predict_cpu(S_new_norm)
        else:
            y_pred, mse_pred = self._predict_cpu(S_new_norm)

        # 反归一化
        y_pred, mse_pred = self._denormalize_prediction(y_pred, mse_pred)

        return y_pred, mse_pred

    def _predict_cpu(self, S_new_norm):
        """CPU版本的预测"""
        m_new = S_new_norm.shape[0]
        y_pred = np.zeros(m_new)
        mse_pred = np.zeros(m_new)

        for i in range(m_new):
            s = S_new_norm[i, :]

            # 计算相关向量
            r = np.zeros(self.S.shape[0])
            for j in range(self.S.shape[0]):
                d = s - self.S[j, :]
                r[j] = self.corrgauss(self.theta, d)

            # 回归向量
            if self.regr == 'regpoly0':
                f = self.regpoly0(s.reshape(1, -1))
            else:
                f = self.regpoly1(s.reshape(1, -1))

            # 预测均值
            rt = solve_triangular(self.C, r, lower=True)
            y_pred[i] = f @ self.beta + rt @ self.gamma

            # 预测方差
            u = solve_triangular(self.G, self.Ft.T @ rt - f.T, lower=False)
            mse_pred[i] = self.sigma2 * (1 - rt @ rt + u @ u)

            # 确保方差非负
            mse_pred[i] = max(mse_pred[i], 0)

        return y_pred, mse_pred


def dacefit(S, Y, regr='regpoly0', corr='corrgauss', theta0=None, lob=None, upb=None, use_gpu=True):
    """
    DACE拟合函数，与MATLAB版本兼容，支持GPU加速

    Args:
        S: 设计点
        Y: 响应值
        regr: 回归函数
        corr: 相关函数
        theta0: 初始相关参数
        lob: 下界
        upb: 上界
        use_gpu: 是否使用GPU加速

    Returns:
        dmodel: 拟合的GP模型
    """
    gp = GaussianProcess(regr=regr, corr=corr, theta0=theta0, lob=lob, upb=upb, use_gpu=use_gpu)
    gp.fit(S, Y)
    return gp


def predictor(S_new, dmodel):
    """
    DACE预测函数，与MATLAB版本兼容
    
    Args:
        S_new: 新的设计点
        dmodel: 拟合的GP模型
    
    Returns:
        y_pred: 预测均值
        dy_pred: 预测梯度 (暂不实现)
        mse_pred: 预测方差
    """
    y_pred, mse_pred = dmodel.predict(S_new)
    dy_pred = None  # 暂不实现梯度
    
    return y_pred, dy_pred, mse_pred

