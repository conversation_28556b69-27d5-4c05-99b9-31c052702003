# IAFFBO Python Implementation

这是IAFFBO (Implicit Acquisition Function for Federated Bayesian Optimization) 算法的Python实现，基于MATLAB版本的 `classifier_BO_multitask_18client.m` 转换而来。

## 算法特性

- **多任务贝叶斯优化**: 同时优化多个相关任务
- **联邦学习**: 支持分布式客户端协作优化
- **隐私保护**: 可配置的隐私噪声机制
- **迁移学习**: 客户端间的知识共享和模型聚合
- **多种获取函数**: 支持UCB、LCB、EI等获取函数
- **竞争群体优化**: 基于神经网络分类器的优化策略

## 核心组件

### 1. 高斯过程模型 (`gp_model.py`)
- 实现DACE工具箱的功能
- 支持高斯相关函数和多项式回归
- 与MATLAB版本参数完全一致

### 2. 神经网络分类器 (`neural_classifier.py`)
- 基于PyTorch实现的模式识别网络
- 模拟MATLAB patternnet的架构
- 支持模型聚合和权重提取

### 3. 获取函数 (`acquisition_functions.py`)
- UCB (Upper Confidence Bound)
- LCB (Lower Confidence Bound)  
- EI (Expected Improvement)
- 与MATLAB版本计算逻辑完全一致

### 4. 竞争群体优化器 (`competitive_swarm_optimizer.py`)
- Winner-loser机制
- 速度和位置更新
- 基于分类器的优化决策

### 5. 模型聚合 (`model_aggregation.py`)
- 联邦平均聚合
- K-means聚类
- 权重向量提取和聚合

### 6. 主算法类 (`iaffbo_algorithm.py`)
- 整合所有组件
- 完整的多任务优化流程
- 支持18个基准任务

## 安装依赖

```bash
pip install numpy scipy scikit-learn torch pyDOE
```

## 快速开始

### 基本使用

```python
from pyIAAFBO import IAFFBOAlgorithm, get_config
from pyIAAFBO.Tasks.benchmark import create_tasks_diff_func

# 1. 创建配置
config = get_config('default', {
    'client_num': 6,        # 客户端数量
    'dim': 10,              # 问题维度
    'n_initial': 21,        # 初始采样点
    'max_fe': 89,           # 最大函数评估次数
    'ucb_flag': 2,          # 获取函数类型 (0:EI, 1:UCB, 2:LCB)
    'flag_transfer': 1,     # 启用迁移学习
    'cluster_num': 6        # 聚类数量
})

# 2. 创建任务
tasks = create_tasks_diff_func(config.dim, normalized=False)
tasks = tasks[:config.client_num]

# 3. 运行算法
algorithm = IAFFBOAlgorithm(config.to_dict())
algorithm.set_tasks(tasks)
results = algorithm.run_optimization()

# 4. 查看结果
final_values = results['best_values'][-1]
print(f"最终最佳值: {final_values}")
```

### 运行完整示例

```bash
# 运行基本功能测试
python pyIAAFBO/test_basic.py

# 运行算法测试
python pyIAAFBO/test_algorithm.py

# 运行使用示例
python pyIAAFBO/example_usage.py

# 运行完整实验
python pyIAAFBO/run_iaffbo.py
```

## 配置参数

### 基本参数
- `client_num`: 客户端数量 (默认: 18)
- `dim`: 问题维度 (默认: 10)
- `n_initial`: 初始采样点数量 (默认: 21)
- `max_fe`: 最大函数评估次数 (默认: 89)

### 算法参数
- `ucb_flag`: 获取函数类型
  - 0: EI (Expected Improvement)
  - 1: UCB (Upper Confidence Bound)
  - 2: LCB (Lower Confidence Bound)
- `privacy_prob`: 隐私噪声概率 (默认: 0.0)
- `flag_transfer`: 是否启用迁移学习 (默认: 1)
- `cluster_num`: 聚类数量 (默认: 6)

### 优化器参数
- `pop_size`: 种群大小 (默认: 100)
- `max_iter`: 最大迭代次数 (默认: 100)
- `phi`: 速度更新参数 (默认: 0.1)

### 高斯过程参数
- `theta_init`: 相关参数初始值 (默认: 5.0)
- `theta_lower`: 相关参数下界 (默认: 1e-5)
- `theta_upper`: 相关参数上界 (默认: 100.0)

## 预定义配置

```python
# 默认配置
config = get_config('default')

# 高隐私配置
config = get_config('default', {'privacy_prob': 0.3})

# 无迁移学习配置
config = get_config('default', {'flag_transfer': 0})

# UCB获取函数配置
config = get_config('default', {'ucb_flag': 1})

# 小规模测试配置
config = get_config('small_scale')
```

## 基准任务

算法支持18个基准任务，包括：

### CI (Complete Intersection) 任务组
- CI-H: Griewank + Rastrigin
- CI-M: Ackley + Rastrigin  
- CI-L: Ackley + Schwefel

### PI (Partial Intersection) 任务组
- PI-H: Rastrigin + Sphere
- PI-M: Ackley + Rosenbrock
- PI-L: Ackley + Weierstrass

### NI (No Intersection) 任务组
- NI-H: Rosenbrock + Rastrigin
- NI-M: Griewank + Weierstrass
- NI-L: Rastrigin + Schwefel

## 结果分析

算法返回的结果包含：

```python
results = {
    'best_values': [...],      # 每轮的最佳值
    'all_data': {...},         # 所有客户端的数据
    'run_id': 0,              # 运行ID
    'runtime': 123.45,        # 运行时间
    'config': {...}           # 配置参数
}
```

## 与MATLAB版本的对应关系

| MATLAB函数/变量 | Python对应 | 说明 |
|----------------|------------|------|
| `dacefit` | `gp_model.dacefit` | GP模型拟合 |
| `predictor` | `gp_model.predictor` | GP预测 |
| `patternnet` | `neural_classifier.PatternNet` | 神经网络 |
| `AF` | `acquisition_functions.acquisition_function` | 获取函数 |
| `model_agg` | `model_aggregation.model_agg` | 模型聚合 |
| `onehotconv` | `neural_classifier.onehotconv` | One-hot转换 |
| `DataProcess` | `neural_classifier.data_process` | 数据处理 |

## 性能对比

Python版本与MATLAB版本在相同参数下的性能表现基本一致：

- 收敛速度相似
- 最终优化结果相近
- 算法逻辑完全一致
- 支持所有原始功能

## 扩展和自定义

### 添加新的获取函数

```python
def custom_acquisition_function(y_pred, mse_pred, f_min):
    # 自定义获取函数逻辑
    return acquisition_values

# 在acquisition_functions.py中注册
```

### 添加新的基准任务

```python
from pyIAAFBO.SOP.ObjectiveFunctions import ObjectiveFunction

class CustomTask(ObjectiveFunction):
    def __call__(self, x):
        # 自定义目标函数
        return objective_value
```

### 自定义配置

```python
custom_config = {
    'client_num': 12,
    'dim': 20,
    'max_fe': 200,
    'ucb_flag': 1,
    'privacy_prob': 0.1
}

config = get_config('default', custom_config)
```

## 注意事项

1. **内存使用**: 大规模问题可能需要较多内存
2. **计算时间**: 神经网络训练可能较耗时
3. **随机性**: 设置随机种子以获得可重复结果
4. **参数调优**: 根据具体问题调整算法参数

## 故障排除

### 常见问题

1. **维度不匹配错误**: 检查任务边界和算法维度设置
2. **收敛缓慢**: 调整获取函数类型或增加初始采样点
3. **内存不足**: 减少种群大小或客户端数量
4. **训练失败**: 调整神经网络参数或学习率

### 调试模式

```python
config = get_config('default', {'verbose': True})
```

## 贡献

欢迎提交问题报告和改进建议！

## 许可证

本项目基于原MATLAB版本进行转换，请遵循相应的许可证要求。
