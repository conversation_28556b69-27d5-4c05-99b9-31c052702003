"""
IAFFBO算法完整实验脚本 - NIID2设置
运行参数：20轮、10维、niid2（每维度2个分区）
"""

import os
import sys
import numpy as np
import time
import pickle
from datetime import datetime

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from pyIAAFBO.Tasks.benchmark import create_tasks_diff_func
from pyIAAFBO.config import get_config, validate_config, print_config
from pyIAAFBO.iaffbo_algorithm import IAFFBOAlgorithm
from pyIAAFBO.utils.partitions import create_normalized_partitions
from pyIAAFBO.utils.initialize import init_samples


def create_niid2_tasks(dim=10, client_num=18):
    """
    创建NIID2任务设置
    
    Args:
        dim: 维度
        client_num: 客户端数量
        
    Returns:
        tasks: 任务列表
        partitions: 分区信息
    """
    print(f"创建NIID2任务设置: {client_num}个客户端, {dim}维")
    
    # 创建基础任务
    tasks = create_tasks_diff_func(dim, normalized=False)
    
    # 确保任务数量匹配
    if len(tasks) < client_num:
        print(f"警告: 只有{len(tasks)}个任务可用，但需要{client_num}个客户端")
        # 重复使用任务直到满足客户端数量
        while len(tasks) < client_num:
            tasks.extend(tasks[:min(len(tasks), client_num - len(tasks))])
    
    tasks = tasks[:client_num]
    
    # 创建NIID2分区 (每个维度分成2个分区)
    np_per_dim = 2  # niid2中的"2"
    partitions = create_normalized_partitions(client_num, np_per_dim, dim)
    
    print(f"创建了{len(tasks)}个任务")
    print(f"分区设置: 每维度{np_per_dim}个分区")
    print(f"分区示例 (前3个客户端):")
    for i in range(min(3, len(partitions))):
        print(f"  客户端{i+1}: {partitions[i]}")
    
    return tasks, partitions


def run_single_experiment_niid2(config, tasks, partitions, run_id=0):
    """
    运行单次NIID2实验
    
    Args:
        config: 配置对象
        tasks: 任务列表
        partitions: 分区信息
        run_id: 运行ID
        
    Returns:
        results: 实验结果
    """
    print(f"\n{'='*20} Run {run_id + 1} (NIID2) {'='*20}")
    
    # 设置随机种子
    if config.random_seed is not None:
        np.random.seed(config.random_seed + run_id)
    
    # 创建算法实例
    algorithm = IAFFBOAlgorithm(config.to_dict())
    
    # 设置任务
    algorithm.set_tasks(tasks)
    
    # 为每个客户端设置NIID2分区的初始采样
    print("设置NIID2分区采样...")
    for client_id in range(len(tasks)):
        task = tasks[client_id]
        partition = partitions[client_id]

        # 检查任务维度
        task_dim = getattr(task, 'd', config.dim)
        if task_dim != config.dim:
            print(f"警告: 客户端{client_id}的任务维度({task_dim})与配置维度({config.dim})不匹配，跳过")
            continue

        # 使用分区进行非IID采样
        try:
            initial_x = init_samples(
                xlb=task.x_lb,
                xub=task.x_ub,
                normalized=False,
                size=config.n_initial,
                d=config.dim,
                iid=False,  # 非IID采样
                partitions=partition
            )

            # 评估初始点
            initial_y = np.array([task(x) for x in initial_x])

            print(f"客户端{client_id}: 成功生成{len(initial_y)}个样本")

        except Exception as e:
            print(f"客户端{client_id}的分区采样失败: {e}，使用随机采样")
            from pyDOE import lhs
            initial_x = lhs(config.dim, samples=config.n_initial)
            # 缩放到任务边界
            if np.isscalar(task.x_lb):
                initial_x = initial_x * (task.x_ub - task.x_lb) + task.x_lb
            else:
                initial_x = initial_x * (np.array(task.x_ub) - np.array(task.x_lb)) + np.array(task.x_lb)

            initial_y = np.array([task(x) for x in initial_x])

        # 更新算法的初始数据
        algorithm.initial_data[client_id] = {
            'x': initial_x[:len(initial_y)],  # 确保x和y长度匹配
            'y': initial_y
        }

        # 记录到评估历史中
        for i, y_val in enumerate(initial_y):
            if len(algorithm.evaluation_history) <= i:
                algorithm.evaluation_history.append({})
            algorithm.evaluation_history[i][client_id] = y_val
    
    # 运行优化（使用GPU优化版本）
    start_time = time.time()
    if hasattr(algorithm, 'run_optimization_gpu_optimized'):
        print("使用GPU优化版本运行...")
        results = algorithm.run_optimization_gpu_optimized()
    else:
        print("使用标准版本运行...")
        results = algorithm.run_optimization_parallel()
    end_time = time.time()

    # 保存真实评估历史为CSV
    csv_filename = algorithm.save_evaluation_history_csv(run_id=run_id + 1)

    # 添加运行信息
    results['run_id'] = run_id
    results['runtime'] = end_time - start_time
    results['config'] = config.to_dict()
    results['csv_filename'] = csv_filename
    results['partitions'] = partitions
    results['experiment_type'] = 'NIID2'

    print(f"Run {run_id + 1} completed in {end_time - start_time:.2f} seconds")
    
    # 打印最佳结果
    if results['best_values']:
        final_best = results['best_values'][-1]
        print(f"最终最佳值 (前5个客户端): {[f'{v:.6f}' for v in final_best[:5]]}")

    return results


def run_niid2_experiment():
    """
    运行完整的NIID2实验
    """
    print("="*80)
    print("IAFFBO算法 - NIID2完整实验")
    print("参数: 20轮, 10维, niid2 (每维度2个分区)")
    print("="*80)
    
    # 实验配置
    custom_params = {
        # 基本实验参数
        'runs': 20,              # 20轮实验
        'client_num': 18,        # 18个客户端
        'dim': 10,               # 10维问题
        
        # 采样参数
        'n_initial': 50,         # 初始采样点数量
        'max_fe': 60,            # 最大函数评估次数
        
        # 算法参数
        'ucb_flag': 2,           # LCB获取函数
        'privacy_prob': 0.0,     # 无隐私噪声
        'flag_transfer': 1,      # 启用迁移学习
        'cluster_num': 6,        # 6个聚类
        
        # 优化器参数
        'pop_size': 100,         # 种群大小
        'max_iter': 100,         # 最大迭代次数
        'phi': 0.1,              # 速度更新参数
        
        # 神经网络参数
        'nn_learning_rate': 0.01,
        'nn_max_epochs': 1000,
        'nn_patience': 50,
        
        # 性能优化参数
        'use_parallel': True,
        'n_workers': 6,
        'use_gpu': True,
        'enable_compile': True,
        'gpu_batch_size': 64,
        
        # 输出控制
        'verbose': True,
        'save_results': True,
        'result_dir': 'results_niid2'
    }
    
    # 获取配置
    config = get_config('default', custom_params)
    
    # 验证配置
    is_valid, error_messages = validate_config(config)
    if not is_valid:
        print("配置验证失败:")
        for msg in error_messages:
            print(f"  - {msg}")
        return None
    
    # 打印配置
    print_config(config)
    
    # 创建NIID2任务和分区
    tasks, partitions = create_niid2_tasks(config.dim, config.client_num)
    
    # 创建结果目录
    os.makedirs(config.result_dir, exist_ok=True)
    
    # 运行多次实验
    all_results = []
    total_start_time = time.time()
    
    print(f"\n开始运行{config.runs}次NIID2实验...")
    
    for run_id in range(config.runs):
        try:
            results = run_single_experiment_niid2(config, tasks, partitions, run_id)
            all_results.append(results)
            
            # 显示进度
            progress = (run_id + 1) / config.runs * 100
            print(f"进度: {progress:.1f}% ({run_id + 1}/{config.runs})")
            
        except Exception as e:
            print(f"Run {run_id + 1} 出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
            continue
    
    total_end_time = time.time()
    total_runtime = total_end_time - total_start_time
    
    print(f"\n所有实验完成!")
    print(f"总运行时间: {total_runtime:.2f} 秒")
    print(f"平均每轮时间: {total_runtime / len(all_results):.2f} 秒")
    print(f"成功完成: {len(all_results)}/{config.runs} 轮")
    
    # 保存结果
    if all_results:
        save_niid2_results(all_results, config, partitions)
    
    return all_results


def save_niid2_results(all_results, config, partitions):
    """
    保存NIID2实验结果
    """
    # 创建结果目录
    os.makedirs(config.result_dir, exist_ok=True)
    
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    experiment_name = f"IAFFBO_NIID2_{config.client_num}clients_D{config.dim}_R{config.runs}"
    
    # 保存完整结果
    result_filename = f"{experiment_name}_{timestamp}.pkl"
    result_filepath = os.path.join(config.result_dir, result_filename)
    
    with open(result_filepath, 'wb') as f:
        pickle.dump({
            'results': all_results,
            'config': config.to_dict(),
            'partitions': partitions,
            'experiment_type': 'NIID2',
            'timestamp': timestamp,
            'experiment_name': experiment_name
        }, f)
    
    print(f"完整结果已保存到: {result_filepath}")
    
    # 保存统计摘要
    save_niid2_summary(all_results, config, partitions, timestamp)


def save_niid2_summary(all_results, config, partitions, timestamp):
    """
    保存NIID2实验统计摘要
    """
    if not all_results:
        return
    
    # 计算统计信息
    n_runs = len(all_results)
    n_clients = config.client_num
    
    # 收集最终最佳值
    final_best_values = []
    for result in all_results:
        if result['best_values']:
            final_values = result['best_values'][-1]
            final_best_values.append(final_values)
    
    if not final_best_values:
        print("警告: 没有有效的结果数据")
        return
    
    final_best_values = np.array(final_best_values)
    
    # 计算统计量
    mean_values = np.mean(final_best_values, axis=0)
    std_values = np.std(final_best_values, axis=0)
    min_values = np.min(final_best_values, axis=0)
    max_values = np.max(final_best_values, axis=0)
    
    # 保存摘要
    summary_filename = f"IAFFBO_NIID2_summary_{timestamp}.txt"
    summary_filepath = os.path.join(config.result_dir, summary_filename)
    
    with open(summary_filepath, 'w', encoding='utf-8') as f:
        f.write("IAFFBO算法 - NIID2实验结果摘要\n")
        f.write("=" * 60 + "\n\n")
        
        f.write(f"实验类型: NIID2 (每维度2个分区)\n")
        f.write(f"时间戳: {timestamp}\n")
        f.write(f"运行次数: {n_runs}\n")
        f.write(f"客户端数量: {n_clients}\n")
        f.write(f"问题维度: {config.dim}\n")
        f.write(f"总评估次数: {config.n_initial + config.max_fe}\n\n")
        
        f.write("算法配置:\n")
        f.write(f"  获取函数: {['EI', 'UCB', 'LCB'][config.ucb_flag]}\n")
        f.write(f"  迁移学习: {'启用' if config.flag_transfer else '禁用'}\n")
        f.write(f"  聚类数量: {config.cluster_num}\n")
        f.write(f"  隐私概率: {config.privacy_prob}\n\n")
        
        f.write("分区设置示例 (前3个客户端):\n")
        for i in range(min(3, len(partitions))):
            f.write(f"  客户端{i+1}: {partitions[i]}\n")
        f.write("\n")
        
        f.write("最终结果统计 (所有运行):\n")
        f.write("-" * 40 + "\n")
        
        for client_id in range(n_clients):
            f.write(f"客户端 {client_id + 1}:\n")
            f.write(f"  均值: {mean_values[client_id]:.6f}\n")
            f.write(f"  标准差: {std_values[client_id]:.6f}\n")
            f.write(f"  最小值: {min_values[client_id]:.6f}\n")
            f.write(f"  最大值: {max_values[client_id]:.6f}\n\n")
        
        f.write("总体统计:\n")
        f.write(f"  全局最佳: {np.min(final_best_values):.6f}\n")
        f.write(f"  全局最差: {np.max(final_best_values):.6f}\n")
        f.write(f"  总体均值: {np.mean(final_best_values):.6f}\n")
        f.write(f"  总体标准差: {np.std(final_best_values):.6f}\n")
    
    print(f"实验摘要已保存到: {summary_filepath}")


if __name__ == "__main__":
    try:
        results = run_niid2_experiment()
        if results:
            print(f"\n🎉 NIID2实验成功完成!")
            print(f"✅ 完成 {len(results)} 轮实验")
            print(f"📁 结果保存在 'results_niid2' 目录")
        else:
            print("❌ 实验失败!")
    except Exception as e:
        print(f"❌ 实验过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
