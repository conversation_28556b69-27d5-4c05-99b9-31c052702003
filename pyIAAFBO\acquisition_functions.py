"""
获取函数实现
包括UCB、LCB、EI等获取函数，与MATLAB版本完全一致
"""

import numpy as np
from scipy.stats import norm


def acquisition_function(pop_obj, mse, a1_obj_norm, ucb_flag):
    """
    获取函数，与MATLAB的AF函数完全一致
    
    Args:
        pop_obj: GP预测均值
        mse: GP预测方差
        a1_obj_norm: 已知最优值（归一化后）
        ucb_flag: 获取函数类型标志
                 0: EI (Expected Improvement)
                 1: UCB (Upper Confidence Bound)
                 2: LCB (Lower Confidence Bound)
    
    Returns:
        objs: 获取函数值
    """
    pop_obj = np.asarray(pop_obj)
    mse = np.asarray(mse)
    
    if ucb_flag == 1:
        # UCB: 上置信界
        objs = pop_obj + 2 * np.sqrt(mse)
        
    elif ucb_flag == 0:
        # EI: 期望改进
        objs = expected_improvement(pop_obj, mse, a1_obj_norm)
        
    elif ucb_flag == 2:
        # LCB: 下置信界
        objs = pop_obj - 2 * np.sqrt(mse)
        
    else:
        raise ValueError(f"Unknown UCB_Flag: {ucb_flag}")
    
    return objs


def expected_improvement(y_pred, mse_pred, f_min):
    """
    期望改进函数
    
    Args:
        y_pred: 预测均值
        mse_pred: 预测方差
        f_min: 当前最优值
    
    Returns:
        ei: 期望改进值（取负值用于最小化）
    """
    y_pred = np.asarray(y_pred)
    mse_pred = np.asarray(mse_pred)
    
    # 计算标准差
    s = np.sqrt(mse_pred)
    
    # 计算改进量
    improvement = f_min - y_pred
    
    # 避免除零
    ei = np.zeros_like(y_pred)
    
    # 当标准差为0时的处理
    zero_std_mask = (s == 0)
    nonzero_std_mask = ~zero_std_mask
    
    # 标准差为0的情况
    ei[zero_std_mask] = np.maximum(improvement[zero_std_mask], 0)
    
    # 标准差不为0的情况
    if np.any(nonzero_std_mask):
        z = improvement[nonzero_std_mask] / s[nonzero_std_mask]
        ei[nonzero_std_mask] = (
            improvement[nonzero_std_mask] * norm.cdf(z) + 
            s[nonzero_std_mask] * norm.pdf(z)
        )
    
    # 返回负值用于最小化
    return -ei


def upper_confidence_bound(y_pred, mse_pred, kappa=2.0):
    """
    上置信界函数
    
    Args:
        y_pred: 预测均值
        mse_pred: 预测方差
        kappa: 置信参数，默认为2.0
    
    Returns:
        ucb: 上置信界值
    """
    return y_pred + kappa * np.sqrt(mse_pred)


def lower_confidence_bound(y_pred, mse_pred, kappa=2.0):
    """
    下置信界函数
    
    Args:
        y_pred: 预测均值
        mse_pred: 预测方差
        kappa: 置信参数，默认为2.0
    
    Returns:
        lcb: 下置信界值
    """
    return y_pred - kappa * np.sqrt(mse_pred)


def probability_of_improvement(y_pred, mse_pred, f_min, xi=0.01):
    """
    改进概率函数
    
    Args:
        y_pred: 预测均值
        mse_pred: 预测方差
        f_min: 当前最优值
        xi: 探索参数
    
    Returns:
        pi: 改进概率
    """
    y_pred = np.asarray(y_pred)
    mse_pred = np.asarray(mse_pred)
    
    s = np.sqrt(mse_pred)
    
    # 避免除零
    pi = np.zeros_like(y_pred)
    nonzero_mask = (s > 0)
    
    if np.any(nonzero_mask):
        z = (f_min - xi - y_pred[nonzero_mask]) / s[nonzero_mask]
        pi[nonzero_mask] = norm.cdf(z)
    
    return pi


def entropy_search(y_pred, mse_pred, samples=1000):
    """
    熵搜索获取函数
    
    Args:
        y_pred: 预测均值
        mse_pred: 预测方差
        samples: 蒙特卡洛样本数
    
    Returns:
        es: 熵搜索值
    """
    # 简化实现，实际应用中可能需要更复杂的计算
    return np.sqrt(mse_pred)


def knowledge_gradient(y_pred, mse_pred, current_best):
    """
    知识梯度获取函数
    
    Args:
        y_pred: 预测均值
        mse_pred: 预测方差
        current_best: 当前最优值
    
    Returns:
        kg: 知识梯度值
    """
    # 简化实现
    improvement = np.maximum(current_best - y_pred, 0)
    uncertainty = np.sqrt(mse_pred)
    
    return improvement + uncertainty


class AcquisitionOptimizer:
    """
    获取函数优化器
    """
    
    def __init__(self, acquisition_func, bounds, n_restarts=10):
        """
        初始化优化器
        
        Args:
            acquisition_func: 获取函数
            bounds: 变量边界
            n_restarts: 重启次数
        """
        self.acquisition_func = acquisition_func
        self.bounds = bounds
        self.n_restarts = n_restarts
    
    def optimize(self, gp_model, n_candidates=1000):
        """
        优化获取函数
        
        Args:
            gp_model: 高斯过程模型
            n_candidates: 候选点数量
        
        Returns:
            best_x: 最优点
            best_value: 最优值
        """
        # 生成候选点
        dim = len(self.bounds)
        candidates = np.random.uniform(
            low=[b[0] for b in self.bounds],
            high=[b[1] for b in self.bounds],
            size=(n_candidates, dim)
        )
        
        # 评估候选点
        y_pred, mse_pred = gp_model.predict(candidates)
        acquisition_values = self.acquisition_func(y_pred, mse_pred)
        
        # 找到最优点
        best_idx = np.argmax(acquisition_values)
        best_x = candidates[best_idx]
        best_value = acquisition_values[best_idx]
        
        return best_x, best_value


# 与MATLAB版本兼容的函数别名
AF = acquisition_function  # 与MATLAB的AF函数对应
