"""
竞争群体优化算法实现
基于MATLAB版本的竞争群体优化逻辑
优化版本：支持GPU加速和并行计算
"""

import numpy as np
import torch
from concurrent.futures import ThreadPoolExecutor
from .neural_classifier import onehotconv


class CompetitiveSwarmOptimizer:
    """
    竞争群体优化器
    """
    
    def __init__(self, bounds, pop_size=100, max_iter=100, phi=0.1, use_gpu=True, batch_size=64):
        """
        初始化优化器

        Args:
            bounds: 变量边界 [lower_bound, upper_bound]
            pop_size: 种群大小
            max_iter: 最大迭代次数
            phi: 速度更新参数
            use_gpu: 是否使用GPU加速
            batch_size: 批处理大小
        """
        self.bounds = np.array(bounds)
        self.lower_bound = self.bounds[0]
        self.upper_bound = self.bounds[1]
        self.pop_size = pop_size
        self.max_iter = max_iter
        self.phi = phi
        self.use_gpu = use_gpu and torch.cuda.is_available()
        self.batch_size = batch_size
        self.device = torch.device('cuda' if self.use_gpu else 'cpu')
        self.dim = len(self.lower_bound) if hasattr(self.lower_bound, '__len__') else 1

        # 确保边界是数组形式
        if np.isscalar(self.lower_bound):
            self.lower_bound = np.full(self.dim, self.lower_bound)
        if np.isscalar(self.upper_bound):
            self.upper_bound = np.full(self.dim, self.upper_bound)

        # 初始化种群
        self.population = None
        self.velocities = None
        
    def initialize_population(self, existing_solutions=None):
        """
        初始化种群
        
        Args:
            existing_solutions: 已有解，将被包含在初始种群中
        """
        if existing_solutions is not None:
            existing_solutions = np.array(existing_solutions)
            if existing_solutions.ndim == 1:
                existing_solutions = existing_solutions.reshape(1, -1)
            
            n_existing = existing_solutions.shape[0]
            n_random = max(0, self.pop_size - n_existing)
            
            # 生成随机解
            if n_random > 0:
                random_solutions = np.random.uniform(
                    self.lower_bound, self.upper_bound, 
                    size=(n_random, self.dim)
                )
                self.population = np.vstack([random_solutions, existing_solutions])
            else:
                self.population = existing_solutions[:self.pop_size]
        else:
            # 完全随机初始化
            self.population = np.random.uniform(
                self.lower_bound, self.upper_bound, 
                size=(self.pop_size, self.dim)
            )
        
        # 确保种群大小为偶数
        if self.pop_size % 2 == 1:
            # 添加一个随机个体
            new_individual = np.random.uniform(
                self.lower_bound, self.upper_bound, size=(1, self.dim)
            )
            self.population = np.vstack([new_individual, self.population])
            self.pop_size = self.population.shape[0]
        
        # 随机打乱种群
        shuffle_idx = np.random.permutation(self.pop_size)
        self.population = self.population[shuffle_idx]
        
        # 初始化速度
        self.velocities = np.zeros_like(self.population)
    
    def clip_to_bounds(self, solutions):
        """
        将解限制在边界内
        
        Args:
            solutions: 解向量
            
        Returns:
            clipped_solutions: 限制后的解向量
        """
        return np.clip(solutions, self.lower_bound, self.upper_bound)
    
    def optimize(self, classifier, existing_solutions=None):
        """
        执行优化，支持GPU加速批处理

        Args:
            classifier: 神经网络分类器，用于评估winner-loser关系
            existing_solutions: 已有解

        Returns:
            best_solution: 最优解
        """
        # 初始化种群
        self.initialize_population(existing_solutions)

        # 主优化循环
        for iteration in range(self.max_iter):
            # 随机排列种群
            rank = np.random.permutation(self.pop_size)
            half_pop = self.pop_size // 2

            losers = rank[:half_pop]
            winners = rank[half_pop:]

            # 构造比较对（向量化操作）
            loser_solutions = self.population[losers]
            winner_solutions = self.population[winners]
            comparison_pairs = np.concatenate([loser_solutions, winner_solutions], axis=1)

            # 使用分类器预测winner-loser关系
            # 输入需要归一化到[0,1]
            lower_bounds_extended = np.tile(self.lower_bound, 2)
            upper_bounds_extended = np.tile(self.upper_bound, 2)

            normalized_pairs = (comparison_pairs - lower_bounds_extended) / \
                              (upper_bounds_extended - lower_bounds_extended)

            # 使用批处理预测以提高GPU利用率
            if self.use_gpu and hasattr(classifier, 'predict') and len(normalized_pairs) > 10:
                predictions = classifier.predict(normalized_pairs, batch_size=self.batch_size)
            else:
                predictions = classifier.predict(normalized_pairs)

            # 根据预测结果调整winner-loser分配
            replace_indices = np.where(predictions == -1)[0]

            # 交换被预测为需要替换的对
            temp_losers = losers[replace_indices].copy()
            losers[replace_indices] = winners[replace_indices]
            winners[replace_indices] = temp_losers
            
            # 更新losers的位置和速度（向量化操作）
            loser_positions = self.population[losers]
            winner_positions = self.population[winners]
            loser_velocities = self.velocities[losers]

            # 使用GPU加速向量化计算
            if self.use_gpu and half_pop > 50:
                new_positions, new_velocities = self._gpu_update_positions(
                    loser_positions, winner_positions, loser_velocities, half_pop
                )
            else:
                new_positions, new_velocities = self._cpu_update_positions(
                    loser_positions, winner_positions, loser_velocities, half_pop
                )

            # 更新种群
            self.population[losers] = new_positions
            self.velocities[losers] = new_velocities

            # 保持winners不变，但更新它们的速度为0（如MATLAB版本）
            winner_velocities = self.velocities[winners]
            self.velocities[winners] = winner_velocities  # 保持原有速度

            # 重新组合种群
            self.population = np.vstack([self.population[winners], self.population[losers]])
            self.velocities = np.vstack([self.velocities[winners], self.velocities[losers]])

        # 随机选择一个解作为结果（与MATLAB版本一致）
        best_idx = np.random.randint(0, self.pop_size)
        best_solution = self.population[best_idx]

        return best_solution

    def _cpu_update_positions(self, loser_positions, winner_positions, loser_velocities, half_pop):
        """CPU版本的位置更新"""
        # 生成随机数
        r1 = np.random.rand(half_pop, 1)
        r2 = np.random.rand(half_pop, 1)
        r3 = np.random.rand(half_pop, 1)

        # 计算种群中心
        population_mean = np.mean(self.population, axis=0)

        # 速度更新公式（与MATLAB版本一致）
        new_velocities = (r1 * loser_velocities +
                        self.phi * r2 * (winner_positions - loser_positions) +
                        r3 * (1 - 0) * (population_mean - loser_positions))

        # 位置更新
        new_positions = loser_positions + new_velocities

        # 边界处理
        new_positions = self.clip_to_bounds(new_positions)

        return new_positions, new_velocities

    def _gpu_update_positions(self, loser_positions, winner_positions, loser_velocities, half_pop):
        """GPU版本的位置更新"""
        try:
            # 转换为GPU tensor
            loser_pos_gpu = torch.tensor(loser_positions, dtype=torch.float32, device=self.device)
            winner_pos_gpu = torch.tensor(winner_positions, dtype=torch.float32, device=self.device)
            loser_vel_gpu = torch.tensor(loser_velocities, dtype=torch.float32, device=self.device)
            pop_mean_gpu = torch.tensor(np.mean(self.population, axis=0), dtype=torch.float32, device=self.device)

            # 生成随机数
            r1 = torch.rand(half_pop, 1, device=self.device)
            r2 = torch.rand(half_pop, 1, device=self.device)
            r3 = torch.rand(half_pop, 1, device=self.device)

            # 速度更新公式
            new_velocities_gpu = (r1 * loser_vel_gpu +
                                self.phi * r2 * (winner_pos_gpu - loser_pos_gpu) +
                                r3 * (pop_mean_gpu.unsqueeze(0) - loser_pos_gpu))

            # 位置更新
            new_positions_gpu = loser_pos_gpu + new_velocities_gpu

            # 边界处理
            lower_bound_gpu = torch.tensor(self.lower_bound, dtype=torch.float32, device=self.device)
            upper_bound_gpu = torch.tensor(self.upper_bound, dtype=torch.float32, device=self.device)
            new_positions_gpu = torch.clamp(new_positions_gpu, lower_bound_gpu, upper_bound_gpu)

            # 转换回CPU numpy数组
            new_positions = new_positions_gpu.cpu().numpy()
            new_velocities = new_velocities_gpu.cpu().numpy()

            return new_positions, new_velocities

        except Exception as e:
            # GPU计算失败时回退到CPU
            return self._cpu_update_positions(loser_positions, winner_positions, loser_velocities, half_pop)


def competitive_swarm_optimization(bounds, classifier, pop_size=100, max_iter=100, 
                                 phi=0.1, existing_solutions=None):
    """
    竞争群体优化函数接口
    
    Args:
        bounds: 变量边界
        classifier: 神经网络分类器
        pop_size: 种群大小
        max_iter: 最大迭代次数
        phi: 速度参数
        existing_solutions: 已有解
        
    Returns:
        best_solution: 最优解
    """
    optimizer = CompetitiveSwarmOptimizer(
        bounds=bounds,
        pop_size=pop_size,
        max_iter=max_iter,
        phi=phi
    )
    
    return optimizer.optimize(classifier, existing_solutions)


class PairwiseDataGenerator:
    """
    成对数据生成器，用于训练分类器
    """
    
    def __init__(self):
        pass
    
    def generate_pairwise_data(self, solutions, objectives, privacy_noise_prob=0.0):
        """
        生成成对比较数据
        
        Args:
            solutions: 解向量
            objectives: 目标函数值
            privacy_noise_prob: 隐私噪声概率
            
        Returns:
            X_pairs: 成对特征
            y_pairs: 成对标签
        """
        solutions = np.array(solutions)
        objectives = np.array(objectives).flatten()
        
        n_solutions = len(solutions)
        
        # 生成所有可能的配对
        pairs_x = []
        pairs_y = []
        
        for i in range(n_solutions):
            for j in range(i + 1, n_solutions):
                # 创建特征对
                pair_features = np.concatenate([solutions[i], solutions[j]])
                pairs_x.append(pair_features)
                
                # 创建标签
                if objectives[i] - objectives[j] > 0:
                    label = 1  # i比j差
                elif objectives[i] - objectives[j] < 0:
                    label = -1  # i比j好
                else:
                    label = 0  # 相等
                
                pairs_y.append(label)
        
        pairs_x = np.array(pairs_x)
        pairs_y = np.array(pairs_y)
        
        # 随机打乱
        shuffle_idx = np.random.permutation(len(pairs_y))
        pairs_x = pairs_x[shuffle_idx]
        pairs_y = pairs_y[shuffle_idx]
        
        # 添加隐私噪声
        if privacy_noise_prob > 0:
            n_noise = int(privacy_noise_prob * len(pairs_y))
            noise_indices = np.random.choice(len(pairs_y), n_noise, replace=False)
            
            # 随机翻转标签
            noise_labels = np.random.choice([-1, 1], n_noise)
            pairs_y[noise_indices] = pairs_y[noise_indices] * noise_labels
        
        return pairs_x, pairs_y
