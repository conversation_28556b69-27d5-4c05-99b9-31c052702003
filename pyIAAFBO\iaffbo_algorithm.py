"""
IAFFBO算法主类
整合所有组件，实现完整的多任务贝叶斯优化流程
"""

import numpy as np
import torch
from pyDOE import lhs
from sklearn.cluster import KMeans
import copy
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp
from functools import lru_cache
import gc

from .gp_model import dacefit, predictor
from .neural_classifier import NeuralClassifier, data_process, onehotconv
from .acquisition_functions import acquisition_function
from .competitive_swarm_optimizer import CompetitiveSwarmOptimizer, PairwiseDataGenerator
from .model_aggregation import ModelAggregator, ModelClusterer, FederatedAggregationStrategy


class IAFFBOAlgorithm:
    """
    IAFFBO算法主类
    """
    
    def __init__(self, config):
        """
        初始化算法
        
        Args:
            config: 配置字典，包含所有算法参数
        """
        # 基本参数
        self.client_num = config.get('client_num', 18)
        self.dim = config.get('dim', 10)
        self.n_initial = config.get('n_initial', 21)
        self.max_fe = config.get('max_fe', 89)
        self.runs = config.get('runs', 20)
        
        # 算法参数
        self.ucb_flag = config.get('ucb_flag', 2)  # 0:EI, 1:UCB, 2:LCB
        self.privacy_prob = config.get('privacy_prob', 0.0)
        self.cluster_num = config.get('cluster_num', 6)
        self.flag_transfer = config.get('flag_transfer', 1)
        
        # 优化器参数
        self.pop_size = config.get('pop_size', 100)
        self.max_iter = config.get('max_iter', 100)
        self.phi = config.get('phi', 0.1)
        
        # GP参数
        self.theta_init = config.get('theta_init', 5.0)
        self.theta_lower = config.get('theta_lower', 1e-5)
        self.theta_upper = config.get('theta_upper', 100.0)
        
        # 神经网络参数
        self.nn_learning_rate = config.get('nn_learning_rate', 0.01)
        self.nn_max_epochs = config.get('nn_max_epochs', 1000)
        self.nn_patience = config.get('nn_patience', 50)
        
        # 初始化组件
        self.gp_models = {}
        self.neural_classifiers = {}
        self.theta_params = {}
        self.aggregator = ModelAggregator()
        self.clusterer = ModelClusterer(n_clusters=self.cluster_num)
        self.fed_strategy = FederatedAggregationStrategy()
        self.data_generator = PairwiseDataGenerator()
        
        # 数据存储
        self.initial_data = {}
        self.new_data = {}
        self.fail_counts = {}

        # 真实评估值记录 (用于保存CSV)
        self.evaluation_history = []  # 每次真实函数评估的记录
        
        # 任务信息
        self.tasks = None
        self.task_bounds = {}
        
        # 并行处理参数
        self.use_parallel = config.get('use_parallel', True)
        self.n_workers = config.get('n_workers', min(mp.cpu_count(), self.client_num))
        
        # 缓存参数
        self.enable_cache = config.get('enable_cache', True)
        self.cache_size = config.get('cache_size', 128)
    
    def set_tasks(self, tasks):
        """
        设置优化任务
        
        Args:
            tasks: 任务列表
        """
        self.tasks = tasks
        
        # 提取任务边界
        for i, task in enumerate(tasks):
            if hasattr(task, 'x_bound'):
                bounds = task.x_bound
                if isinstance(bounds, list) and len(bounds) == 2:
                    self.task_bounds[i] = [bounds[0], bounds[1]]
                else:
                    self.task_bounds[i] = [task.x_lb, task.x_ub]
            else:
                self.task_bounds[i] = [task.x_lb, task.x_ub]
    
    def initialize_clients(self):
        """
        初始化所有客户端
        """
        for client_id in range(self.client_num):
            # 初始化GP参数
            self.theta_params[client_id] = self.theta_init * np.ones(self.dim)
            
            # 初始化失败计数
            self.fail_counts[client_id] = 0
            
            # 生成初始数据
            task = self.tasks[client_id]
            bounds = self.task_bounds[client_id]
            
            # LHS采样
            initial_x = lhs(self.dim, samples=self.n_initial)
            
            # 缩放到任务边界
            if np.isscalar(bounds[0]):
                initial_x = initial_x * (bounds[1] - bounds[0]) + bounds[0]
            else:
                initial_x = initial_x * (np.array(bounds[1]) - np.array(bounds[0])) + np.array(bounds[0])
            
            # 随机打乱
            shuffle_idx = np.random.permutation(self.n_initial)
            initial_x = initial_x[shuffle_idx]
            
            # 评估初始点
            initial_y = np.array([task(x) for x in initial_x])

            # 记录初始评估到历史记录中
            for i, y_val in enumerate(initial_y):
                if len(self.evaluation_history) <= i:
                    self.evaluation_history.append({})
                self.evaluation_history[i][client_id] = y_val

            self.initial_data[client_id] = {
                'x': initial_x,
                'y': initial_y
            }
            
            self.new_data[client_id] = {
                'x': np.empty((0, self.dim)),
                'y': np.empty(0)
            }
    
    def update_gp_model(self, client_id):
        """
        更新客户端的GP模型
        
        Args:
            client_id: 客户端ID
        """
        # 合并数据
        all_x = np.vstack([
            self.initial_data[client_id]['x'],
            self.new_data[client_id]['x']
        ]) if self.new_data[client_id]['x'].size > 0 else self.initial_data[client_id]['x']
        
        all_y = np.concatenate([
            self.initial_data[client_id]['y'],
            self.new_data[client_id]['y']
        ]) if self.new_data[client_id]['y'].size > 0 else self.initial_data[client_id]['y']
        
        # 去除重复点
        unique_indices = []
        seen = set()
        for i, x in enumerate(all_x):
            x_tuple = tuple(x)
            if x_tuple not in seen:
                seen.add(x_tuple)
                unique_indices.append(i)
        
        all_x = all_x[unique_indices]
        all_y = all_y[unique_indices]
        
        # 归一化目标值
        y_min, y_max = np.min(all_y), np.max(all_y)
        if y_max > y_min:
            all_y_norm = (all_y - y_min) / (y_max - y_min)
        else:
            all_y_norm = np.zeros_like(all_y)
        
        # 拟合GP模型
        theta0 = self.theta_params[client_id]
        lob = self.theta_lower * np.ones(self.dim)
        upb = self.theta_upper * np.ones(self.dim)
        
        gp_model = dacefit(all_x, all_y_norm, 'regpoly0', 'corrgauss', theta0, lob, upb)
        
        # 更新参数
        self.gp_models[client_id] = gp_model
        self.theta_params[client_id] = gp_model.theta
        
        # 存储归一化参数
        gp_model.y_min = y_min
        gp_model.y_max = y_max
    
    def train_neural_classifier(self, client_id, cluster_assignments=None):
        """
        训练神经网络分类器
        
        Args:
            client_id: 客户端ID
            cluster_assignments: 聚类分配结果
        """
        # 合并数据
        all_x = np.vstack([
            self.initial_data[client_id]['x'],
            self.new_data[client_id]['x']
        ]) if self.new_data[client_id]['x'].size > 0 else self.initial_data[client_id]['x']
        
        all_y = np.concatenate([
            self.initial_data[client_id]['y'],
            self.new_data[client_id]['y']
        ]) if self.new_data[client_id]['y'].size > 0 else self.initial_data[client_id]['y']
        
        # 生成候选点用于预测
        bounds = self.task_bounds[client_id]
        n_candidates = 100
        
        if np.isscalar(bounds[0]):
            candidates = np.random.uniform(bounds[0], bounds[1], size=(n_candidates, self.dim))
        else:
            candidates = np.random.uniform(
                np.array(bounds[0]), np.array(bounds[1]), 
                size=(n_candidates, self.dim)
            )
        
        # GP预测
        gp_model = self.gp_models[client_id]
        pred_y, _, pred_mse = predictor(candidates, gp_model)
        
        # 反归一化
        if hasattr(gp_model, 'y_min') and hasattr(gp_model, 'y_max'):
            if gp_model.y_max > gp_model.y_min:
                pred_y = pred_y * (gp_model.y_max - gp_model.y_min) + gp_model.y_min
                pred_mse = pred_mse * (gp_model.y_max - gp_model.y_min)**2
        
        # 计算获取函数值
        y_min_norm = 0.0  # 归一化后的最小值
        acq_values = acquisition_function(pred_y, pred_mse, y_min_norm, self.ucb_flag)
        
        # 合并已知数据和候选数据
        combined_x = np.vstack([all_x, candidates])
        combined_acq = np.concatenate([all_y, acq_values])
        
        # 生成成对数据
        pairs_x, pairs_y = self.data_generator.generate_pairwise_data(
            combined_x, combined_acq, self.privacy_prob
        )

        # 数据处理
        train_x, train_y, test_x, test_y = data_process(pairs_x, pairs_y)

        # 创建或更新分类器
        # 输入维度应该是 2 * self.dim (两个解连接)
        input_dim = 2 * self.dim
        
        if client_id not in self.neural_classifiers:
            # 创建新分类器
            classifier = NeuralClassifier(
                input_dim=input_dim,
                learning_rate=self.nn_learning_rate,
                max_epochs=self.nn_max_epochs,
                patience=self.nn_patience
            )
        else:
            # 使用现有分类器或聚合模型
            if self.flag_transfer and cluster_assignments is not None:
                # 使用聚合策略
                partner_ids = self.fed_strategy.select_aggregation_partners(
                    self.neural_classifiers, client_id, 
                    list(range(self.client_num)), cluster_assignments
                )
                
                if len(partner_ids) > 1:
                    # 聚合模型
                    classifier_copy = self.aggregator.aggregate_models(
                        self.neural_classifiers, partner_ids
                    )
                    classifier = NeuralClassifier(
                        input_dim=input_dim,
                        learning_rate=self.nn_learning_rate,
                        max_epochs=self.nn_max_epochs,
                        patience=self.nn_patience
                    )
                    classifier.net = classifier_copy.net if hasattr(classifier_copy, 'net') else classifier_copy
                else:
                    classifier = self.neural_classifiers[client_id]
            else:
                classifier = self.neural_classifiers[client_id]
        
        # 训练分类器
        classifier.train(train_x, train_y, test_x, test_y)
        
        # 保存分类器
        self.neural_classifiers[client_id] = classifier
    
    @lru_cache(maxsize=128)
    def _cached_acquisition_function(self, x_tuple, client_id):
        """缓存获取函数计算结果"""
        x = np.array(x_tuple)
        return self._compute_acquisition_value(x, client_id)
    
    def optimize_acquisition(self, client_id):
        """
        优化获取函数
        
        Args:
            client_id: 客户端ID
            
        Returns:
            new_x: 新的候选点
        """
        bounds = self.task_bounds[client_id]
        classifier = self.neural_classifiers[client_id]
        
        # 准备现有解
        existing_solutions = None
        if self.new_data[client_id]['x'].size > 0:
            existing_solutions = self.new_data[client_id]['x']
        
        # 竞争群体优化
        # 确保边界是数组格式
        if np.isscalar(bounds[0]):
            lower_bounds = np.full(self.dim, bounds[0])
            upper_bounds = np.full(self.dim, bounds[1])
        else:
            lower_bounds = np.array(bounds[0])
            upper_bounds = np.array(bounds[1])

        optimizer = CompetitiveSwarmOptimizer(
            bounds=[lower_bounds, upper_bounds],
            pop_size=self.pop_size,
            max_iter=self.max_iter,
            phi=self.phi
        )
        
        new_x = optimizer.optimize(classifier, existing_solutions)
        
        # 定期清理内存
        if hasattr(self, '_round_counter'):
            self._round_counter += 1
            if self._round_counter % 10 == 0:
                gc.collect()
                if self.enable_cache:
                    self._cached_acquisition_function.cache_clear()
        else:
            self._round_counter = 1
        
        return new_x
    
    def _process_client_parallel(self, client_id):
        """并行处理单个客户端"""
        # 更新GP模型
        self.update_gp_model(client_id)
        
        # 训练分类器
        self.train_classifier(client_id)
        
        # 优化获取函数
        new_x = self.optimize_acquisition(client_id)
        
        return client_id, new_x
    
    def run_optimization_parallel(self):
        """并行版本的优化过程"""
        self.initialize_clients()
        
        results = {'best_values': [], 'all_data': {}}
        
        for round_idx in range(self.max_fe):
            print(f"Round {round_idx + 1}/{self.max_fe}")
            
            if self.use_parallel and self.client_num > 1:
                # 并行处理所有客户端
                with ThreadPoolExecutor(max_workers=self.n_workers) as executor:
                    futures = [executor.submit(self._process_client_parallel, client_id) 
                              for client_id in range(self.client_num)]
                    
                    round_results = []
                    for future in futures:
                        client_id, new_x = future.result()
                        round_results.append((client_id, new_x))
            else:
                # 串行处理（原始逻辑）
                round_results = []
                for client_id in range(self.client_num):
                    new_x = self._process_client_parallel(client_id)
                    round_results.append((client_id, new_x))
            
            # 处理结果...
            for client_id, new_x in round_results:
                # 评估新点
                task = self.tasks[client_id]
                new_y = task(new_x)

                # 记录到评估历史中
                evaluation_round = self.n_initial + round_idx
                if len(self.evaluation_history) <= evaluation_round:
                    self.evaluation_history.append({})
                self.evaluation_history[evaluation_round][client_id] = new_y

                # 更新数据
                if self.new_data[client_id]['x'].size == 0:
                    self.new_data[client_id]['x'] = new_x.reshape(1, -1)
                    self.new_data[client_id]['y'] = np.array([new_y])
                else:
                    self.new_data[client_id]['x'] = np.vstack([
                        self.new_data[client_id]['x'], new_x.reshape(1, -1)
                    ])
                    self.new_data[client_id]['y'] = np.append(
                        self.new_data[client_id]['y'], new_y
                    )
                
                # 更新失败计数
                all_y = np.concatenate([
                    self.initial_data[client_id]['y'],
                    self.new_data[client_id]['y']
                ])
                
                current_best = np.min(all_y)
                if len(all_y) >= 2 and current_best == np.min(all_y[:-1]):
                    self.fail_counts[client_id] += 1
                else:
                    self.fail_counts[client_id] = 0
                
                round_best_values.append(current_best)
            
            results['best_values'].append(round_best_values)
            
            # 打印进度
            print(f"Best value for client 0: {round_best_values[0]:.6f}")
        
        # 保存最终数据
        for client_id in range(self.client_num):
            all_x = np.vstack([
                self.initial_data[client_id]['x'],
                self.new_data[client_id]['x']
            ]) if self.new_data[client_id]['x'].size > 0 else self.initial_data[client_id]['x']
            
            all_y = np.concatenate([
                self.initial_data[client_id]['y'],
                self.new_data[client_id]['y']
            ]) if self.new_data[client_id]['y'].size > 0 else self.initial_data[client_id]['y']
            
            results['all_data'][client_id] = {
                'x': all_x,
                'y': all_y
            }
        
        return results

    def save_evaluation_history_csv(self, filename=None, run_id=1):
        """
        保存真实评估历史为CSV文件

        Args:
            filename: 文件名，如果为None则自动生成
            run_id: 运行ID
        """
        import pandas as pd
        import time

        if filename is None:
            # 自动生成文件名: run{run_id}_IAFFBO_{client_num}.{total_evaluations}_init{n_initial}_FE{max_fe}_AF{ucb_flag}.csv
            total_evaluations = self.n_initial + self.max_fe
            af_names = {0: 'EI', 1: 'UCB', 2: 'LCB'}
            af_name = af_names.get(self.ucb_flag, str(self.ucb_flag))

            filename = f"run{run_id}_IAFFBO_{self.client_num}.{total_evaluations}_init{self.n_initial}_FE{self.max_fe}_{af_name}.csv"

        # 创建DataFrame
        data = []

        for eval_idx, eval_data in enumerate(self.evaluation_history):
            row = [eval_idx]  # 第一列是评估索引

            # 添加每个客户端的值
            for client_id in range(self.client_num):
                if client_id in eval_data:
                    row.append(eval_data[client_id])
                else:
                    row.append(None)  # 如果某个客户端没有数据

            # 添加时间戳 (模拟)
            row.append(time.time())

            data.append(row)

        # 创建列名
        columns = [''] + [f'client{i}' for i in range(self.client_num)] + ['time']

        # 创建DataFrame并保存
        df = pd.DataFrame(data, columns=columns)
        df.to_csv(filename, index=False)

        print(f"评估历史已保存到: {filename}")
        print(f"总评估次数: {len(self.evaluation_history)}")
        print(f"客户端数量: {self.client_num}")

        return filename



