"""
IAFFBO (Implicit Acquisition Function for Federated Bayesian Optimization) Package

This package implements the IAFFBO algorithm for multi-task Bayesian optimization
with federated learning capabilities, converted from MATLAB to Python.

Main Components:
- IAFFBOAlgorithm: Main algorithm class
- GaussianProcess: GP model implementation
- NeuralClassifier: Neural network classifier
- CompetitiveSwarmOptimizer: CSO optimization
- ModelAggregator: Federated model aggregation
- Config: Configuration management

Usage:
    from pyIAAFBO import IAFFBOAlgorithm, get_config
    from pyIAAFBO.Tasks.benchmark import create_tasks_diff_func
    
    # Create configuration
    config = get_config('default')
    
    # Create tasks
    tasks = create_tasks_diff_func(dim=10)
    
    # Run algorithm
    algorithm = IAFFBOAlgorithm(config.to_dict())
    algorithm.set_tasks(tasks)
    results = algorithm.run_optimization()
"""

from .iaffbo_algorithm import IAFFBOAlgorithm
from .config import get_config, IAFFBOConfig, validate_config, print_config
from .gp_model import GaussianProcess, dacefit, predictor
from .neural_classifier import NeuralClassifier, PatternNet, data_process, onehotconv
from .acquisition_functions import acquisition_function, expected_improvement, upper_confidence_bound, lower_confidence_bound
from .competitive_swarm_optimizer import CompetitiveSwarmOptimizer, competitive_swarm_optimization, PairwiseDataGenerator
from .model_aggregation import ModelAggregator, ModelClusterer, FederatedAggregationStrategy, model_agg

__version__ = "1.0.0"
__author__ = "IAFFBO Team"
__email__ = "<EMAIL>"

__all__ = [
    # Main algorithm
    'IAFFBOAlgorithm',
    
    # Configuration
    'get_config',
    'IAFFBOConfig', 
    'validate_config',
    'print_config',
    
    # Gaussian Process
    'GaussianProcess',
    'dacefit',
    'predictor',
    
    # Neural Classifier
    'NeuralClassifier',
    'PatternNet',
    'data_process',
    'onehotconv',
    
    # Acquisition Functions
    'acquisition_function',
    'expected_improvement',
    'upper_confidence_bound',
    'lower_confidence_bound',
    
    # Competitive Swarm Optimizer
    'CompetitiveSwarmOptimizer',
    'competitive_swarm_optimization',
    'PairwiseDataGenerator',
    
    # Model Aggregation
    'ModelAggregator',
    'ModelClusterer',
    'FederatedAggregationStrategy',
    'model_agg',
]
