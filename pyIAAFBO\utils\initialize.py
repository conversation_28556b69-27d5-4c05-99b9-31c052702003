# coding=utf-8
import numpy as np
from pyDOE import lhs


def init_samples(xlb, xub, normalized, size, d, iid, partitions):
    if iid:
        # LHS sampling
        result = lhs(d, samples=size)
    else:
        # 非IID采样：使用分区边界
        # partitions格式: [lb, ub]，其中lb和ub都是长度为d的列表
        if len(partitions) != 2:
            raise ValueError(f"分区应该有2个边界列表[lb, ub]，但有{len(partitions)}个")

        lb, ub = partitions
        if len(lb) != d or len(ub) != d:
            raise ValueError(f"分区边界长度({len(lb)}, {len(ub)})与维度({d})不匹配")

        # 为每个维度在其分区内生成样本
        samples_per_dim = []
        for dim_idx in range(d):
            # 在该维度的分区内均匀采样
            dim_samples = np.random.uniform(lb[dim_idx], ub[dim_idx], size=size)
            samples_per_dim.append(dim_samples)

        # 转置以获得正确的形状 (size, d)
        result = np.array(samples_per_dim).T

    if normalized:
        return result
    else:
        return result * (xub - xlb) + xlb


def uniform_samples(xlb, xub, normalized, size, d):
    if normalized:
        return np.random.uniform(low=0, high=1, size=(size, d))
    else:
        return np.random.uniform(low=xlb, high=xub, size=(size, d))
